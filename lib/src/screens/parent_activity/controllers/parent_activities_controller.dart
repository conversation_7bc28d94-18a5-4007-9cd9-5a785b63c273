import 'package:connectify_app/src/screens/parent_activity/models/parent_activities_response_model.dart';
import 'package:connectify_app/src/screens/parent_activity/repos/parent_activities_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * =========================================================

final parentActivitiesControllerProvider =
    ChangeNotifierProvider.family<ParentActivitiesController, BuildContext>(
        (ref, context) {
  final parentActivitiesRepo = ref.watch(parentActivitiesRepoProvider);

  return ParentActivitiesController(
    parentActivitiesRepo: parentActivitiesRepo,
    context: context,
  );
});

// Provider for getting all parent activities data
final getParentActivitiesDataProvider =
    FutureProvider.family<ParentActivitiesResponseModel, BuildContext>(
        (ref, context) {
  final parentActivitiesRepo = ref.watch(parentActivitiesRepoProvider);

  final controller = ParentActivitiesController(
    parentActivitiesRepo: parentActivitiesRepo,
    context: context,
  );

  return controller.getParentActivitiesData();
});

// Provider for getting parent activities data by date
final getParentActivitiesByDateProvider = FutureProvider.family<
    ParentActivitiesResponseModel,
    (BuildContext, String, String)>((ref, params) {
  final (context, dateString, dayName) = params;
  final parentActivitiesRepo = ref.watch(parentActivitiesRepoProvider);

  final controller = ParentActivitiesController(
    parentActivitiesRepo: parentActivitiesRepo,
    context: context,
  );

  final date = DateTime.parse(dateString);

  return controller.getParentActivitiesByDate(date: date, dayName: dayName);
});

class ParentActivitiesController extends BaseVM {
  final ParentActivitiesRepo parentActivitiesRepo;
  final BuildContext context;

  ParentActivitiesController({
    required this.parentActivitiesRepo,
    required this.context,
  });

  Future<ParentActivitiesResponseModel> getParentActivitiesData({
    Map<String, dynamic>? filters,
    String? sort,
  }) async {
    return await baseFunction(context, () async {
      final parentActivitiesData =
          await parentActivitiesRepo.getParentActivitiesData(
        filters: filters,
        sort: sort ?? 'createdAt:desc',
      );

      return parentActivitiesData;
    });
  }

  Future<ParentActivitiesResponseModel> getParentActivitiesByDate({
    required DateTime date,
    String? dayName,
  }) async {
    return await baseFunction(context, () async {
      final parentActivitiesData =
          await parentActivitiesRepo.getParentActivitiesByDate(
        date: date,
        dayName: dayName,
      );

      return parentActivitiesData;
    });
  }

  Future<ParentActivitiesResponseModel> getParentActivitiesWithFilters({
    Map<String, dynamic>? customFilters,
    String? sort,
  }) async {
    return await baseFunction(context, () async {
      final parentActivitiesData =
          await parentActivitiesRepo.getParentActivitiesData(
        filters: customFilters,
        sort: sort ?? 'createdAt:desc',
      );

      return parentActivitiesData;
    });
  }

  Future<ParentActivitiesResponseModel> getParentActivitiesByClass({
    required int classId,
    String? sort,
  }) async {
    return await baseFunction(context, () async {
      final filters = <String, dynamic>{
        'class': {'id': classId},
      };

      final parentActivitiesData =
          await parentActivitiesRepo.getParentActivitiesData(
        filters: filters,
        sort: sort ?? 'createdAt:desc',
      );

      return parentActivitiesData;
    });
  }
}
