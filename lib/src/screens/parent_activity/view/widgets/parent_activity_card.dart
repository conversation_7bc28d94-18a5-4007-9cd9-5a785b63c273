import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/food/model/food_model.dart';
import 'package:connectify_app/src/screens/parent_activity/view/widgets/teacher_activity_media_widget.dart';
import 'package:connectify_app/src/screens/sleep/model/sleep_model.dart';
import 'package:connectify_app/src/screens/toilet/model/toilet_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../generated/assets.dart';
import '../../../activity_level/model/activity_level_model.dart';
import '../../../mood/model/mood_model.dart';
import '../../models/teacher_activity_model.dart';

class ParentActivityCard extends HookConsumerWidget {
  final dynamic
      activityModel; // dynamic to handle multiple models like FoodModel, ToiletModel, TeacherActivityModel
  final String selectedDate;

  const ParentActivityCard({
    super.key,
    required this.activityModel,
    required this.selectedDate,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              padding: const EdgeInsets.all(2.0),
              margin: const EdgeInsets.only(top: 5.0),
              width: 20.0,
              height: 20.0,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ColorManager.white,
                border: Border.all(
                  color: ColorManager.primaryColor,
                  width: 1,
                ),
              ),
              child: const CircleAvatar(
                backgroundColor: ColorManager.primaryColor,
                radius: 1,
              ),
            ),
            CustomPaint(
              size: Size(0, 100.h),
              painter: LinePainter(),
            ),
          ],
        ),
        context.smallGap,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getActivityTime(activityModel),
                style: context.boldTitle,
              ),
              context.mediumGap,
              _buildActivityDetails(context),
            ],
          ),
        ),
      ],
    );
  }

  // Method to handle different types of activities
  String _getActivityTime(dynamic model) {
    if (model is TeacherActivityModel) {
      return '${model.startTime} / ${model.endTime}';
    } else if (model is FoodModel) {
      return model.createdAt?.formatTime ?? '';
    } else if (model is ToiletModel) {
      return model.createdAt?.formatTime ?? '';
    } else if (model is SleepModel) {
      return '${model.sleepStartTime} - ${model.sleepEndTime}';
    } else if (model is MoodModel) {
      return model.createdAt?.formatTime ?? '';
    } else if (model is ActivityLevelModel) {
      return model.createdAt?.formatTime ?? '';
    } else {
      return '';
    }
  }

  // Widget to build specific details for each activity type
  Widget _buildActivityDetails(BuildContext context) {
    if (activityModel is TeacherActivityModel) {
      return _buildTeacherActivityCard(
          activityModel as TeacherActivityModel, context);
    } else if (activityModel is FoodModel) {
      return _buildFoodActivityCard(activityModel as FoodModel, context);
    } else if (activityModel is ToiletModel) {
      return _buildToiletActivityCard(activityModel as ToiletModel, context);
    } else if (activityModel is SleepModel) {
      return _buildSleepActivityCard(activityModel as SleepModel, context);
    } else if (activityModel is MoodModel) {
      return _buildMoodActivityCard(activityModel as MoodModel, context);
    } else if (activityModel is ActivityLevelModel) {
      return _buildActivityLevelCard(
          activityModel as ActivityLevelModel, context);
    } else {
      return const SizedBox.shrink();
    }
  }

  // Build Sleep Activity Card
  Widget _buildSleepActivityCard(SleepModel model, BuildContext context) {
    return BaseContainer(
        child: Row(
      children: [
        Image.asset(
          Assets.imagesSleep,
          width: 50,
          height: 50,
        ),
        context.mediumGap,
        Text(
          context.tr.sleep,
          style: context.title,
        ),
      ],
    ));
  }

  // Build Teacher Activity Card
  Widget _buildTeacherActivityCard(
      TeacherActivityModel model, BuildContext context) {
    final notesModel = model.notes.firstWhereOrNull(
      (element) => element.date == selectedDate,
    );
    return BaseContainer(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              SizedBox(
                height: 50.h,
                width: 55.w,
                child: ClipRRect(
                  borderRadius:
                      BorderRadius.circular(AppRadius.baseContainerRadius),
                  child: BaseCachedImage(
                    model.activity?.image?.url ?? '',
                    fit: BoxFit.cover,
                    errorWidget: Image.asset(Assets.imagesActivities),
                  ),
                ),
              ),
              context.smallGap,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GestureDetector(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            backgroundColor: Colors.white,
                            surfaceTintColor: Colors.white,
                            title: Text(model.activity?.name ?? ''),
                            content: SingleChildScrollView(
                              child: ListBody(
                                children: <Widget>[
                                  if (model.activity?.description != null &&
                                      model.activity?.description != '')
                                    Text(model.activity?.description ?? ''),
                                  if (notesModel?.note.isNotEmpty ?? false) ...[
                                    const SizedBox(height: 10),
                                    Text(
                                        '${context.tr.note}: ${notesModel?.note ?? ''}'),
                                  ],
                                ],
                              ),
                            ),
                            actions: <Widget>[
                              TextButton(
                                child: Text(context.tr.back),
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                              ),
                            ],
                          );
                        },
                      );
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: context.width * 0.33,
                          child: Text(
                            model.activity?.name ?? '',
                            style: context.blueHint,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (model.activity?.description != null &&
                            model.activity?.description != '') ...[
                          context.xSmallGap,
                          SizedBox(
                            width: context.width * 0.33,
                            child: Text(
                              model.activity?.description ?? '',
                              style: context.smallHint.copyWith(fontSize: 12),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                        if (notesModel?.note.isNotEmpty ?? false) ...[
                          context.smallGap,
                          SizedBox(
                            width: context.width * 0.33,
                            child: Text(
                              '${context.tr.note}: ${notesModel?.note ?? ''}',
                              style: context.smallHint.copyWith(fontSize: 12),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
          Column(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: model.isWeekly
                      ? ColorManager.primaryColor
                      : ColorManager.blueColor,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(AppRadius.baseRadius),
                  ),
                ),
                child: Text(
                  model.isWeekly
                      ? context.tr.weeklyActivity
                      : context.tr.singleActivity,
                  style: context.whiteHint,
                ),
              ),
              if (notesModel?.media.isNotEmpty ?? false) ...[
                IconButton(
                  icon: const Icon(
                    CupertinoIcons.photo_on_rectangle,
                  ),
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      showDragHandle: true,
                      builder: (context) {
                        return TeacherActivityMediaWidget(
                          mediaList: notesModel?.media ?? [],
                        );
                      },
                    );
                  },
                )
              ],
            ],
          ),
        ],
      ),
    );
  }

  // Build Food Activity Card
  Widget _buildFoodActivityCard(FoodModel model, BuildContext context) {
    return BaseContainer(
      child: Row(
        children: [
          Image.asset(
            Assets.imagesFood,
            width: 50,
            height: 50,
          ),
          context.mediumGap,
          Expanded(
            child: Text(
                '${model.getMealType()} (${model.getMealAmount()})\n${model.meal}',
                style: context.subTitle),
          ),
        ],
      ),
    );
  }

  // Build Toilet Activity Card
  Widget _buildToiletActivityCard(ToiletModel model, BuildContext context) {
    return BaseContainer(
      child: Row(
        children: [
          Image.asset(
            Assets.imagesToilet,
            width: 50,
            height: 50,
          ),
          context.mediumGap,
          Row(
            children: [
              Text(
                '${model.toiletType?.name} -' ?? '',
                style: context.title,
              ),
              context.smallGap,
              Text(
                model.toiletWay?.name ?? '',
                style: context.title,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build Mood Activity Card
  Widget _buildMoodActivityCard(MoodModel model, BuildContext context) {
    return BaseContainer(
      child: Row(
        children: [
          const Icon(
            Icons.mood,
            size: 50,
            color: ColorManager.primaryColor,
          ),
          context.smallGap,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr.mood,
                  style: context.title,
                ),
                context.xSmallGap,
                Text(
                  model.getMoodContextLang(context),
                  style: context.hint,
                ),
                if (model.note?.isNotEmpty ?? false) ...[
                  context.xSmallGap,
                  Text(
                    model.note ?? '',
                    style: context.smallHint,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build Activity Level Card
  Widget _buildActivityLevelCard(
      ActivityLevelModel model, BuildContext context) {
    return BaseContainer(
      child: Row(
        children: [
          const Icon(
            Icons.trending_up,
            size: 50,
            color: ColorManager.primaryColor,
          ),
          context.smallGap,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr.activityLevel,
                  style: context.title,
                ),
                context.xSmallGap,
                Text(
                  model.getActivityLevelContextLang(context),
                  style: context.hint,
                ),
                if (model.note?.isNotEmpty ?? false) ...[
                  context.xSmallGap,
                  Text(
                    model.note ?? '',
                    style: context.smallHint,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = ColorManager.primaryColor
      ..strokeWidth = 2;

    canvas.drawLine(
        Offset(size.width / 2, 0), Offset(size.width / 2, size.height), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

// class ParentActivityCard extends HookConsumerWidget {
//   final TeacherActivityModel teacherActivity;
//
//   const ParentActivityCard({
//     super.key,
//     required this.teacherActivity,
//   });
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Row(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Column(
//           children: [
//             Container(
//               padding: const EdgeInsets.all(2.0),
//               margin: const EdgeInsets.only(top: 5.0),
//               width: 20.0,
//               height: 20.0,
//               decoration: BoxDecoration(
//                 shape: BoxShape.circle,
//                 color: ColorManager.white,
//                 border: Border.all(
//                   color: ColorManager.primaryColor,
//                   width: 1,
//                 ),
//               ),
//               child: const CircleAvatar(
//                 backgroundColor: ColorManager.primaryColor,
//                 radius: 1,
//               ),
//             ),
//             CustomPaint(
//               size: Size(0, 100.h),
//               painter: LinePainter(),
//             ),
//           ],
//         ),
//         context.smallGap,
//         Expanded(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text('${teacherActivity.startTime} / ${teacherActivity.endTime}',
//                   style: context.boldTitle),
//               context.mediumGap,
//               BaseContainer(
//                 child: Row(
//                   children: [
//                     SizedBox(
//                       height: 50.h,
//                       width: 55.w,
//                       child: ClipRRect(
//                         borderRadius: BorderRadius.circular(
//                             AppRadius.baseContainerRadius),
//                         child: BaseCachedImage(
//                           teacherActivity.activity?.image?.url ?? '',
//                           fit: BoxFit.cover,
//                           errorWidget: Image.asset(
//                             Assets.imagesActivities,
//                           ),
//                         ),
//                       ),
//                     ),
//                     context.smallGap,
//                     Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           teacherActivity.activity?.name ?? '',
//                           style: context.blueHint,
//                           maxLines: 1,
//                         ),
//                         context.xSmallGap,
//                         Text(
//                           teacherActivity.activity?.description ?? '',
//                           style: context.smallHint.copyWith(fontSize: 12),
//                           maxLines: 2,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
// }

// class LinePainter extends CustomPainter {
//   @override
//   void paint(Canvas canvas, Size size) {
//     final paint = Paint()
//       ..color = ColorManager.primaryColor
//       ..strokeWidth = 2;
//
//     canvas.drawLine(
//         Offset(size.width / 2, 0), Offset(size.width / 2, size.height), paint);
//   }
//
//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) {
//     return false;
//   }
// }
