import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/home_payment/payment_dialog_widget.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class HomePaymentCardWidget extends StatelessWidget {
  const HomePaymentCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<StudentModel?>(
      valueListenable: selectedStudent,
      builder: (context, studentData, child) {
        return StatefulBuilder(builder: (context, setState) {
          final student = selectedStudent.value;
          // rebuild each sec if student is null
          if (student == null) {
            Future.delayed(const Duration(seconds: 1), () {
              setState(() {});
            });
          }

          if (student == null) {
            return const LoadingWidget();
          }

          final cutoffDate = DateTime(2025, 5, 15); // May 15, 2025

          // CONDITION 1: If student subscriptionDate is null, don't show payment card
          if (student.subscriptionDate == null) {
            return const SizedBox.shrink();
          }

          // Handle case where student has subscriptionDate but no subscription history (first payment)
          final bool isFirstPayment = student.subscriptions.isEmpty;

          DateTime? latestSubscription;

          if (!isFirstPayment) {
            // Get valid subscriptions (with non-empty dates)
            final validSubscriptions = student.subscriptions
                .where((sub) => sub.date.isNotEmpty)
                .toList();

            if (validSubscriptions.isNotEmpty) {
              // Get the latest subscription date
              latestSubscription = validSubscriptions
                  .map((sub) => sub.date.formatStringToDateTime)
                  .reduce((a, b) => a.isAfter(b) ? a : b);

              // CONDITION 3: If latest subscription is before cutoff date (May 15, 2025), don't show payment card
              if (latestSubscription.isBefore(cutoffDate)) {
                return const SizedBox.shrink();
              }
            }
          }

          // For first payment, we'll use subscriptionDate in the payment calculation below

          // Calculate next payment date based on subscriptionDate or latest subscription
          final DateTime nextPaymentDate;

          if (student.subscriptionDate != null) {
            // Use subscriptionDate as base for payment calculation
            final now = DateTime.now();
            final subscriptionDay = student.subscriptionDate!.day;

            if (isFirstPayment) {
              // For first payment, use subscriptionDate directly
              nextPaymentDate = student.subscriptionDate!;
            } else {
              // Calculate next payment date based on subscription day
              DateTime calculatedDate =
                  DateTime(now.year, now.month, subscriptionDay);

              // If the subscription day has already passed this month, move to next month
              if (calculatedDate.isBefore(now) ||
                  calculatedDate.isAtSameMomentAs(now)) {
                calculatedDate =
                    DateTime(now.year, now.month + 1, subscriptionDay);
              }

              nextPaymentDate = calculatedDate;
            }
          } else if (latestSubscription != null) {
            // Fallback: Use one month after latest subscription
            nextPaymentDate = DateTime(
              latestSubscription.year,
              latestSubscription.month + 1,
              latestSubscription.day,
            );
          } else {
            // This shouldn't happen since we check subscriptionDate != null above
            nextPaymentDate = DateTime.now();
          }

          // Check for current period subscription (already paid AND approved)
          final currentPeriodSubscription =
              student.subscriptions.lastWhereOrNull(
            (element) {
              final paymentDate = element.date.formatStringToDateTime;
              return paymentDate.year == nextPaymentDate.year &&
                  paymentDate.month == nextPaymentDate.month &&
                  element.isPaid &&
                  element.isApproved;
            },
          );

          // Check for pending payment request (submitted but not approved yet)
          final pendingPaymentRequest = student.subscriptions.lastWhereOrNull(
            (element) {
              if (isFirstPayment) {
                // For first payment, check if there's any pending payment with screenshot
                return !element.isPaid && !element.isApproved;
              } else {
                // For regular payments, check for current period
                final paymentDate = element.date.formatStringToDateTime;
                return paymentDate.year == nextPaymentDate.year &&
                    paymentDate.month == nextPaymentDate.month &&
                    !element.isPaid &&
                    !element.isApproved;
              }
            },
          );

          Log.w('afaffawfw ${pendingPaymentRequest?.toJson()}');

          // If already paid and approved for current period, check if we should show reminder
          // if (currentPeriodSubscription != null) {
          //   final now = DateTime.now();
          //   final dayBeforeNextPayment =
          //       nextPaymentDate.subtract(const Duration(days: 1));
          //
          //   // Only show card one day before next payment date
          //   if (now.isBefore(dayBeforeNextPayment)) {
          //     return const SizedBox.shrink();
          //   }
          // }

          final amount = student.fees ?? 0;
          return BaseContainer(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.payment,
                      color: pendingPaymentRequest != null
                          ? Colors.blue
                          : currentPeriodSubscription != null
                              ? Colors.green
                              : Colors.orange,
                      size: 24,
                    ),
                    context.smallGap,
                    Expanded(
                      child: Text(
                        pendingPaymentRequest != null
                            ? context.tr.paymentPending
                            : currentPeriodSubscription != null
                                ? context.tr.paymentPaid
                                : context.tr.paymentDue,
                        style: context.boldTitle.copyWith(
                          color: pendingPaymentRequest != null
                              ? Colors.blue
                              : currentPeriodSubscription != null
                                  ? Colors.green
                                  : Colors.orange,
                        ),
                      ),
                    ),
                    Text(
                      '\$$amount',
                      style: context.priceTitle,
                    ),
                  ],
                ),
                context.smallGap,
                Text(
                  student.name,
                  style: context.blueHint.copyWith(fontWeight: FontWeight.bold),
                ),
                context.xSmallGap,
                Text(
                  pendingPaymentRequest != null
                      ? context.tr.paymentPendingApproval
                      : currentPeriodSubscription != null
                          ? '${context.tr.nextPayment}: ${DateTime(nextPaymentDate.year, nextPaymentDate.month + 1, nextPaymentDate.day).formatDateToString}'
                          : '${context.tr.due}: ${nextPaymentDate.formatDateToString}',
                  style: context.hint.copyWith(
                    fontSize: 12,
                    color: pendingPaymentRequest != null
                        ? Colors.blue
                        : currentPeriodSubscription != null
                            ? Colors.green
                            : Colors.orange,
                  ),
                ),
                if (pendingPaymentRequest?.paymentMethod != null) ...[
                  context.xSmallGap,
                  Text(
                    '${context.tr.paymentMethod}: ${pendingPaymentRequest!.paymentMethod}',
                    style: context.hint.copyWith(
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
                context.mediumGap,
                if (pendingPaymentRequest == null &&
                    currentPeriodSubscription == null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => PaymentDialogWidget(
                            student: student,
                            amount: amount,
                            nextPaymentDate: nextPaymentDate,
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorManager.buttonColor,
                        foregroundColor: Colors.white,
                      ),
                      child: Text(context.tr.payNow),
                    ),
                  ),
                if (currentPeriodSubscription != null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Calculate next month's payment date
                        final nextMonthPaymentDate = DateTime(
                          nextPaymentDate.year,
                          nextPaymentDate.month + 1,
                          nextPaymentDate.day,
                        );

                        showDialog(
                          context: context,
                          builder: (context) => PaymentDialogWidget(
                            student: student,
                            amount: amount,
                            nextPaymentDate: nextMonthPaymentDate,
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorManager.buttonColor,
                        foregroundColor: Colors.white,
                      ),
                      child: Text(context.tr.payNow),
                    ),
                  ),
              ],
            ),
          );
        });
      },
    );
  }
}
