import 'package:connectify_app/src/screens/evaluation/model/evaluation_model.dart';
import 'package:connectify_app/src/screens/evaluation/view/parent_evaluation_answer_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';

import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationDialog extends StatelessWidget {
  final List<EvaluationModel> pendingEvaluations;

  const EvaluationDialog({
    super.key,
    required this.pendingEvaluations,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent dismissing by back button
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            const Icon(
              Icons.assignment,
              color: ColorManager.primaryColor,
              size: 28,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                context.tr.newEvaluationAvailable,
                style: context.title.copyWith(
                  fontWeight: FontWeight.bold,
                  color: ColorManager.primaryColor,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.pleaseAnswerEvaluationQuestions,
              style: context.body,
            ),
            const SizedBox(height: 16),

            // Evaluation count info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ColorManager.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: ColorManager.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      context.tr.pendingEvaluations(pendingEvaluations.length),
                      style: context.body.copyWith(
                        color: ColorManager.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Evaluation details
            if (pendingEvaluations.isNotEmpty) ...[
              Text(
                '${context.tr.evaluations}:',
                style: context.body.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              ...pendingEvaluations.take(3).map((evaluation) {
                final questionCount = evaluation.questions.length;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(
                        Icons.circle,
                        size: 6,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '${evaluation.date} ($questionCount ${context.tr.evaluationQuestions})',
                          style: context.hint.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }),
              if (pendingEvaluations.length > 3)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    '... ${context.tr.and} ${pendingEvaluations.length - 3} ${context.tr.more}',
                    style: context.hint.copyWith(
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
            ],
          ],
        ),
        actions: [
          // Answer Now Button
          SizedBox(
            height: 50,
            child: Button(
              label: context.tr.answerNow,
              onPressed: () async {
                Navigator.of(context).pop();

                // Navigate to evaluation answer screen
                final result = await Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => ParentEvaluationAnswerScreen(
                      evaluations: pendingEvaluations,
                    ),
                  ),
                );

                // If evaluations were completed successfully, don't show dialog again
                if (result == true) {
                  // Evaluations completed
                  Log.i('All evaluations completed successfully');
                }
              },
            ),
          ),
        ],
        actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      ),
    );
  }
}

// Helper function to show the evaluation dialog
Future<void> showEvaluationDialog({
  required BuildContext context,
  required List<EvaluationModel> pendingEvaluations,
}) async {
  if (pendingEvaluations.isEmpty) return;

  return showDialog<void>(
    context: context,
    barrierDismissible: false, // Prevent dismissing by tapping outside
    builder: (BuildContext context) {
      return EvaluationDialog(
        pendingEvaluations: pendingEvaluations,
      );
    },
  );
}
