import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:xr_helper/xr_helper.dart';

//? Text Answer Input Widget ========================================================
class TextAnswerInputWidget extends StatelessWidget {
  final EvaluationQuestionModel question;
  final Function(String) onAnswerChanged;
  final String? errorMessage;

  const TextAnswerInputWidget({
    super.key,
    required this.question,
    required this.onAnswerChanged,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: errorMessage != null
                  ? Colors.red
                  : ColorManager.primaryColor.withOpacity(0.3),
            ),
          ),
          child: TextFormField(
            initialValue: question.answer,
            onChanged: onAnswerChanged,
            maxLines: 4,
            decoration: InputDecoration(
              hintText: context.tr.enterYourAnswerHere,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              hintStyle: context.body.copyWith(
                color: Colors.grey,
              ),
            ),
            style: context.body,
          ),
        ),
        if (errorMessage != null) ...[
          const SizedBox(height: 8),
          Text(
            errorMessage!,
            style: context.hint.copyWith(
              color: Colors.red,
            ),
          ),
        ],
      ],
    );
  }
}

//? Rating Answer Input Widget ========================================================
class RatingAnswerInputWidget extends StatelessWidget {
  final EvaluationQuestionModel question;
  final Function(double) onRatingChanged;
  final String? errorMessage;

  const RatingAnswerInputWidget({
    super.key,
    required this.question,
    required this.onRatingChanged,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: errorMessage != null
                  ? Colors.red
                  : ColorManager.primaryColor.withOpacity(0.3),
            ),
          ),
          child: Column(
            children: [
              RatingBar.builder(
                initialRating: question.rate ?? 0,
                minRating: 0,
                direction: Axis.horizontal,
                allowHalfRating: false,
                itemCount: 5,
                itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                itemBuilder: (context, _) => Icon(
                  Icons.star,
                  color: ColorManager.primaryColor,
                ),
                onRatingUpdate: onRatingChanged,
                glowColor: ColorManager.primaryColor.withOpacity(0.3),
                unratedColor: Colors.grey.withOpacity(0.3),
              ),
              const SizedBox(height: 8),
              Text(
                question.rate != null && question.rate! > 0
                    ? '${question.rate!.toInt()} ${context.tr.outOf} 5'
                    : context.tr.pleaseSelectRating,
                style: context.hint.copyWith(
                  color: question.rate != null && question.rate! > 0
                      ? ColorManager.primaryColor
                      : Colors.grey,
                ),
              ),
            ],
          ),
        ),
        if (errorMessage != null) ...[
          const SizedBox(height: 8),
          Text(
            errorMessage!,
            style: context.hint.copyWith(
              color: Colors.red,
            ),
          ),
        ],
      ],
    );
  }
}

//? Question Card Widget ========================================================
class ParentEvaluationQuestionCard extends StatelessWidget {
  final EvaluationQuestionModel question;
  final int questionNumber;
  final int totalQuestions;
  final Function(EvaluationQuestionModel) onQuestionUpdated;
  final String? errorMessage;

  const ParentEvaluationQuestionCard({
    super.key,
    required this.question,
    required this.questionNumber,
    required this.totalQuestions,
    required this.onQuestionUpdated,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Question Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: ColorManager.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Question $questionNumber of $totalQuestions',
                    style: context.hint.copyWith(
                      color: ColorManager.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Spacer(),
                Icon(
                  question.type == QuestionType.text
                      ? Icons.text_fields
                      : Icons.star_rate,
                  color: ColorManager.primaryColor,
                  size: 20,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Question Text
            Text(
              question.question,
              style: context.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            // Answer Input
            if (question.type == QuestionType.text)
              TextAnswerInputWidget(
                question: question,
                errorMessage: errorMessage,
                onAnswerChanged: (answer) {
                  onQuestionUpdated(question.copyWith(answer: answer));
                },
              )
            else
              RatingAnswerInputWidget(
                question: question,
                errorMessage: errorMessage,
                onRatingChanged: (rating) {
                  onQuestionUpdated(question.copyWith(rate: rating));
                },
              ),
          ],
        ),
      ),
    );
  }
}

//? Progress Indicator Widget ========================================================
class EvaluationProgressWidget extends StatelessWidget {
  final int completed;
  final int total;

  const EvaluationProgressWidget({
    super.key,
    required this.completed,
    required this.total,
  });

  @override
  Widget build(BuildContext context) {
    final progress = total > 0 ? completed / total : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorManager.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.tr.evaluationProgress,
                style: context.subTitle.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                'Completed: $completed of $total',
                style: context.body.copyWith(
                  color: ColorManager.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey.withOpacity(0.3),
            valueColor:
                AlwaysStoppedAnimation<Color>(ColorManager.primaryColor),
            minHeight: 8,
          ),
        ],
      ),
    );
  }
}
