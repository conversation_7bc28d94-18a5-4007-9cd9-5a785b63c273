import 'package:connectify_app/src/screens/evaluation/controller/parent_evaluation_controller.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/screens/evaluation/view/widgets/parent_evaluation_answer_widgets.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ParentEvaluationAnswerScreen extends HookConsumerWidget {
  final List<EvaluationModel> evaluations;

  const ParentEvaluationAnswerScreen({
    super.key,
    required this.evaluations,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller =
        ref.watch(parentEvaluationChangeNotifierControllerProvider(context));

    // Current evaluation index
    final currentEvaluationIndex = useState(0);

    // Current evaluation
    final currentEvaluation = evaluations[currentEvaluationIndex.value];

    // Get questions for current evaluation
    final evaluationQuestions =
        controller.getQuestionsForEvaluation(currentEvaluation);

    // State for current answers
    final currentAnswers = useState<List<EvaluationQuestionModel>>(
      evaluationQuestions.map((q) => q.copyWith()).toList(),
    );

    // Validation errors
    final validationErrors = useState<Map<int, String>>({});

    // Loading state
    final isSubmitting = useState(false);

    // Update current answers when evaluation changes
    useEffect(() {
      final questions = controller.getQuestionsForEvaluation(currentEvaluation);
      currentAnswers.value = questions.map((q) => q.copyWith()).toList();
      validationErrors.value = {};
      return null;
    }, [currentEvaluationIndex.value]);

    void updateQuestion(int index, EvaluationQuestionModel updatedQuestion) {
      final newAnswers =
          List<EvaluationQuestionModel>.from(currentAnswers.value);
      newAnswers[index] = updatedQuestion;
      currentAnswers.value = newAnswers;

      // Clear validation error for this question
      if (validationErrors.value.containsKey(index)) {
        final newErrors = Map<int, String>.from(validationErrors.value);
        newErrors.remove(index);
        validationErrors.value = newErrors;
      }
    }

    bool validateAnswers() {
      final errors = <int, String>{};

      for (int i = 0; i < currentAnswers.value.length; i++) {
        final question = currentAnswers.value[i];
        final errorMessage = controller.getValidationErrorMessage(question);
        if (errorMessage != null) {
          errors[i] = errorMessage;
        }
      }

      validationErrors.value = errors;
      return errors.isEmpty;
    }

    Future<void> submitCurrentEvaluation() async {
      if (!validateAnswers()) {
        context.showBarMessage(context.tr.allQuestionsRequired, isError: true);
        return;
      }

      isSubmitting.value = true;

      try {
        final success = await controller.submitEvaluationAnswers(
          evaluation: currentEvaluation,
          answeredQuestions: currentAnswers.value,
        );

        if (success) {
          // Check if there are more evaluations
          if (currentEvaluationIndex.value < evaluations.length - 1) {
            // Move to next evaluation
            currentEvaluationIndex.value++;
            context.showBarMessage(context.tr.evaluationSubmitted);
          } else {
            // All evaluations completed
            Navigator.of(context)
                .pop(true); // Return true to indicate completion
            context.showBarMessage(context.tr.evaluationAnswerSubmitted);
          }
        }
      } catch (e) {
        context.showBarMessage(e.toString(), isError: true);
      } finally {
        isSubmitting.value = false;
      }
    }

    final progressInfo = controller.getProgressInfo(currentAnswers.value);
    final isLastEvaluation =
        currentEvaluationIndex.value == evaluations.length - 1;

    return PopScope(
      canPop: false, // Prevent back navigation
      child: Scaffold(
        appBar: MainAppBar(
          title: context.tr.answerEvaluation,
          isBackButton: false, // Hide back button
        ),
        body: Column(
          children: [
            // Evaluation Progress
            if (evaluations.length > 1)
              Container(
                padding: const EdgeInsets.all(16),
                color: ColorManager.primaryColor.withOpacity(0.05),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${context.tr.evaluation} ${currentEvaluationIndex.value + 1} ${context.tr.outOf} ${evaluations.length}',
                        style: context.title.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Text(
                      currentEvaluation.date,
                      style: context.body.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),

            // Question Progress
            Padding(
              padding: const EdgeInsets.all(16),
              child: EvaluationProgressWidget(
                completed: progressInfo['completed']!,
                total: progressInfo['total']!,
              ),
            ),

            // Questions List
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: currentAnswers.value.length,
                itemBuilder: (context, index) {
                  final question = currentAnswers.value[index];
                  return ParentEvaluationQuestionCard(
                    question: question,
                    questionNumber: index + 1,
                    totalQuestions: currentAnswers.value.length,
                    errorMessage: validationErrors.value[index],
                    onQuestionUpdated: (updatedQuestion) {
                      updateQuestion(index, updatedQuestion);
                    },
                  );
                },
              ),
            ),

            // Submit Button
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                child: Button(
                  loadingWidget: const LoadingWidget(),
                  label: isLastEvaluation
                      ? context.tr.finishEvaluations
                      : context.tr.nextEvaluation,
                  onPressed:
                      isSubmitting.value ? null : submitCurrentEvaluation,
                  isLoading: isSubmitting.value,
                  icon: Icon(
                    isLastEvaluation ? Icons.check_circle : Icons.arrow_forward,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
