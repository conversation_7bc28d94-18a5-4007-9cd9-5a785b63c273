import 'dart:isolate';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_answer_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:flutter/foundation.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  EvaluationRepo(this._networkApiServices);

  //? Get Evaluations Data ========================================================
  Future<List<EvaluationModel>> getEvaluations() async {
    return await baseFunction(() async {
      if (const UserModel().currentUser.id == null) {
        return <EvaluationModel>[];
      }

      final response = await _networkApiServices.getResponse(
        ApiEndpoints.evaluations(const UserModel().currentUser.id),
      );

      final evaluationData =
          await compute(responseToEvaluationModelList, response);

      return evaluationData;
    });
  }

  //? Add Evaluation ========================================================
  Future<EvaluationModel> addEvaluation({
    required EvaluationModel evaluation,
  }) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.postResponse(
        ApiEndpoints.editDeleteEvaluation,
        body: evaluation.toJson(),
      );

      final evaluationData = EvaluationModel.fromJson(response['data']);

      return evaluationData;
    });
  }

  //? Edit Evaluation ========================================================
  Future<EvaluationModel> editEvaluation({
    required int id,
    required EvaluationModel evaluation,
  }) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.putResponse(
        '${ApiEndpoints.editDeleteEvaluation}/$id',
        data: evaluation.toJson(),
      );

      final evaluationData = EvaluationModel.fromJson(response['data']);

      return evaluationData;
    });
  }

  //? Delete Evaluation ========================================================
  Future<void> deleteEvaluation({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices.deleteResponse(
        '${ApiEndpoints.editDeleteEvaluation}/$id',
      );
    });
  }

  //? Get Evaluation Answers Data ========================================================
  Future<List<EvaluationAnswerModel>> getEvaluationAnswers() async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        ApiEndpoints.evaluationAnswers,
      );

      final evaluationAnswerData =
          await compute(responseToEvaluationAnswerModelList, response);

      return evaluationAnswerData;
    });
  }

  //? Add Evaluation Answer ========================================================
  Future<EvaluationAnswerModel> addEvaluationAnswer({
    required EvaluationAnswerModel evaluationAnswer,
  }) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.postResponse(
        ApiEndpoints.editDeleteEvaluationAnswer,
        body: evaluationAnswer.toJson(),
      );

      final evaluationAnswerData =
          EvaluationAnswerModel.fromJson(response['data']);

      return evaluationAnswerData;
    });
  }

  //? Edit Evaluation Answer ========================================================
  Future<EvaluationAnswerModel> editEvaluationAnswer({
    required int id,
    required EvaluationAnswerModel evaluationAnswer,
  }) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.putResponse(
        '${ApiEndpoints.editDeleteEvaluationAnswer}/$id',
        data: evaluationAnswer.toJson(),
      );

      final evaluationAnswerData =
          EvaluationAnswerModel.fromJson(response['data']);

      return evaluationAnswerData;
    });
  }

  //? Delete Evaluation Answer ========================================================
  Future<void> deleteEvaluationAnswer({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices.deleteResponse(
        '${ApiEndpoints.editDeleteEvaluationAnswer}/$id',
      );
    });
  }
}
