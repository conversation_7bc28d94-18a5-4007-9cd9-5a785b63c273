import 'dart:developer';

import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

List<EvaluationModel> responseToEvaluationModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final evaluations = data.map((e) => EvaluationModel.fromJson(e)).toList();

  return evaluations;
}

enum SendTo {
  allParents,
  classes,
}

extension SendToExtension on SendTo {
  String get value {
    switch (this) {
      case SendTo.allParents:
        return 'all_parents';
      case SendTo.classes:
        return 'class';
    }
  }

  String get displayName {
    switch (this) {
      case SendTo.allParents:
        return 'All Parents';
      case SendTo.classes:
        return 'Specific Classes';
    }
  }

  String get displayNameAr {
    switch (this) {
      case SendTo.allParents:
        return 'جميع الأهالي';
      case SendTo.classes:
        return 'فصول محددة';
    }
  }

  static SendTo fromString(String value) {
    switch (value) {
      case 'all_parents':
        return SendTo.allParents;
      case 'class':
        return SendTo.classes;
      default:
        return SendTo.allParents;
    }
  }
}

class EvaluationModel extends Equatable {
  final int? id;
  final String date;
  final List<ClassModel>? classes;
  final SendTo sendTo;
  final List<EvaluationQuestionModel> questions;
  final List<int> parentIds;
  final DateTime? createdAt;

  const EvaluationModel({
    this.id,
    this.date = '',
    this.classes,
    this.sendTo = SendTo.allParents,
    this.questions = const [],
    this.parentIds = const [],
    this.createdAt,
  });

  factory EvaluationModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    log('Evaluation data: $attributes');

    List<ClassModel> classesList = [];
    if (attributes.containsKey('classes') && attributes['classes'] != null) {
      final classesData = attributes['classes'] as List;
      classesList = classesData
          .map((e) => ClassModel.fromJsonWithOutAttributes(e))
          .toList();
    }

    List<EvaluationQuestionModel> questionsList = [];
    if (attributes.containsKey('questions') &&
        attributes['questions'] != null) {
      final questionsData = attributes['questions'] as List;
      questionsList = questionsData
          .map((e) => EvaluationQuestionModel.fromJson(e))
          .toList();
    }

    List<int> parentIdsList = [];
    if (attributes.containsKey('parent_ids') &&
        attributes['parent_ids'] != null) {
      final parentIdsData = attributes['parent_ids'] as List;
      parentIdsList = parentIdsData.map((e) => e['parent_id'] as int).toList();
    }

    return EvaluationModel(
      id: json[ApiStrings.id],
      date: attributes['date'] ?? '',
      classes: classesList,
      sendTo:
          SendToExtension.fromString(attributes['send_to'] ?? 'all_parents'),
      questions: questionsList,
      parentIds: parentIdsList,
      createdAt: attributes[ApiStrings.createdAt] != null
          ? DateTime.parse(attributes[ApiStrings.createdAt]).toLocal()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // if (id != null) ApiStrings.id: id,
      // 'date': date,
      // 'send_to': sendTo.value,
      // 'questions': questions.map((q) => q.toJson()).toList(),
      // if (classes != null && sendTo == SendTo.classes)
      //   'classes': classes!.map((c) => c.id).toList(),
      // ApiStrings.nursery: NurseryModelHelper.currentNurseryId(),
      if (parentIds.isNotEmpty)
        'parent_ids': parentIds.map((id) => {'parent_id': id}).toList(),
    };
  }

  EvaluationModel copyWith({
    int? id,
    String? date,
    List<ClassModel>? classes,
    SendTo? sendTo,
    List<EvaluationQuestionModel>? questions,
    List<int>? parentIds,
    DateTime? createdAt,
  }) {
    return EvaluationModel(
      id: id ?? this.id,
      date: date ?? this.date,
      classes: classes ?? this.classes,
      sendTo: sendTo ?? this.sendTo,
      questions: questions ?? this.questions,
      parentIds: parentIds ?? this.parentIds,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        date,
        classes,
        sendTo,
        questions,
        parentIds,
        createdAt,
      ];
}
