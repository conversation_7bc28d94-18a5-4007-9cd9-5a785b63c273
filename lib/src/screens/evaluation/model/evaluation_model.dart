import 'dart:developer';

import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

List<EvaluationModel> responseToEvaluationModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final evaluations = data.map((e) => EvaluationModel.fromJson(e)).toList();

  return evaluations;
}

enum SendTo {
  allParents,
  classes,
}

extension SendToExtension on SendTo {
  String get value {
    switch (this) {
      case SendTo.allParents:
        return 'all_parents';
      case SendTo.classes:
        return 'class';
    }
  }

  String get displayName {
    switch (this) {
      case SendTo.allParents:
        return 'All Parents';
      case SendTo.classes:
        return 'Specific Classes';
    }
  }

  String get displayNameAr {
    switch (this) {
      case SendTo.allParents:
        return 'جميع الأهالي';
      case SendTo.classes:
        return 'فصول محددة';
    }
  }

  static SendTo fromString(String value) {
    switch (value) {
      case 'all_parents':
        return SendTo.allParents;
      case 'class':
        return SendTo.classes;
      default:
        return SendTo.allParents;
    }
  }
}

// Parent ID with student information
class ParentIdWithStudents {
  final int parentId;
  final List<StudentIdWithClass> studentIds;

  const ParentIdWithStudents({
    required this.parentId,
    required this.studentIds,
  });

  factory ParentIdWithStudents.fromJson(Map<String, dynamic> json) {
    final studentIdsData = json['student_ids'] as List? ?? [];
    final studentIds =
        studentIdsData.map((e) => StudentIdWithClass.fromJson(e)).toList();

    return ParentIdWithStudents(
      parentId: json['parent_id'] as int,
      studentIds: studentIds,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'parent_id': parentId,
      'student_ids': studentIds.map((e) => e.toJson()).toList(),
    };
  }
}

// Student ID with class information
class StudentIdWithClass {
  final int studentId;
  final int classId;

  const StudentIdWithClass({
    required this.studentId,
    required this.classId,
  });

  factory StudentIdWithClass.fromJson(Map<String, dynamic> json) {
    return StudentIdWithClass(
      studentId: json['student_id'] as int,
      classId: json['class_id'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'student_id': studentId,
      'class_id': classId,
    };
  }
}

class EvaluationModel extends Equatable {
  final int? id;
  final String date;
  final List<ClassModel>? classes;
  final SendTo sendTo;
  final List<EvaluationQuestionModel> questions;
  final List<ParentIdWithStudents> parentIds;
  final DateTime? createdAt;

  const EvaluationModel({
    this.id,
    this.date = '',
    this.classes,
    this.sendTo = SendTo.allParents,
    this.questions = const [],
    this.parentIds = const [],
    this.createdAt,
  });

  factory EvaluationModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    log('Evaluation data: ${attributes['parent_ids']}');

    List<ClassModel> classesList = [];
    if (attributes.containsKey('classes') && attributes['classes'] != null) {
      final classesData = attributes['classes'] as List;
      classesList = classesData
          .map((e) => ClassModel.fromJsonWithOutAttributes(e))
          .toList();
    }

    List<EvaluationQuestionModel> questionsList = [];
    if (attributes.containsKey('questions') &&
        attributes['questions'] != null) {
      final questionsData = attributes['questions'] as List;
      questionsList = questionsData
          .map((e) => EvaluationQuestionModel.fromJson(e))
          .toList();
    }

    List<ParentIdWithStudents> parentIdsList = [];
    if (attributes.containsKey('parent_ids') &&
        attributes['parent_ids'] != null) {
      final parentIdsData = attributes['parent_ids'] as List;
      parentIdsList =
          parentIdsData.map((e) => ParentIdWithStudents.fromJson(e)).toList();
    }

    return EvaluationModel(
      id: json[ApiStrings.id],
      date: attributes['date'] ?? '',
      classes: classesList,
      sendTo:
          SendToExtension.fromString(attributes['send_to'] ?? 'all_parents'),
      questions: questionsList,
      parentIds: parentIdsList,
      createdAt: attributes[ApiStrings.createdAt] != null
          ? DateTime.parse(attributes[ApiStrings.createdAt]).toLocal()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // if (id != null) ApiStrings.id: id,
      // 'date': date,
      // 'send_to': sendTo.value,
      // 'questions': questions.map((q) => q.toJson()).toList(),
      // if (classes != null && sendTo == SendTo.classes)
      //   'classes': classes!.map((c) => c.id).toList(),
      // ApiStrings.nursery: NurseryModelHelper.currentNurseryId(),
      if (parentIds.isNotEmpty)
        'parent_ids': parentIds.map((parentId) => parentId.toJson()).toList(),
    };
  }

  EvaluationModel copyWith({
    int? id,
    String? date,
    List<ClassModel>? classes,
    SendTo? sendTo,
    List<EvaluationQuestionModel>? questions,
    List<ParentIdWithStudents>? parentIds,
    DateTime? createdAt,
  }) {
    return EvaluationModel(
      id: id ?? this.id,
      date: date ?? this.date,
      classes: classes ?? this.classes,
      sendTo: sendTo ?? this.sendTo,
      questions: questions ?? this.questions,
      parentIds: parentIds ?? this.parentIds,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        date,
        classes,
        sendTo,
        questions,
        parentIds,
        createdAt,
      ];
}
