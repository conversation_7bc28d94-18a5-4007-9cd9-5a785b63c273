import 'dart:developer';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

List<EvaluationAnswerModel> responseToEvaluationAnswerModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final evaluationAnswers =
      data.map((e) => EvaluationAnswerModel.fromJson(e)).toList();

  return evaluationAnswers;
}

class EvaluationAnswerModel extends Equatable {
  final int? id;
  final List<EvaluationQuestionModel> answers;
  final String studentName;
  final UserModel? parent;
  final ClassModel? classModel;
  final EvaluationModel? evaluation;
  final DateTime? createdAt;

  const EvaluationAnswerModel({
    this.id,
    this.answers = const [],
    this.studentName = '',
    this.parent,
    this.classModel,
    this.evaluation,
    this.createdAt,
  });

  factory EvaluationAnswerModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    log('Evaluation Answer data: $attributes');
    log('attrrrr $attributes');

    List<EvaluationQuestionModel> answersList = [];
    if (attributes.containsKey('answers') && attributes['answers'] != null) {
      final answersData = attributes['answers'] as List;
      answersList =
          answersData.map((e) => EvaluationQuestionModel.fromJson(e)).toList();
    }

    UserModel? parentUser;
    if (attributes.containsKey('parent') &&
        attributes['parent'] != null &&
        attributes['parent'][ApiStrings.data] != null &&
        attributes['parent'][ApiStrings.data][ApiStrings.attributes] != null) {
      parentUser = UserModel.fromJson(
          attributes['parent'][ApiStrings.data]['attributes']);
    }

    ClassModel? classModel;
    if (attributes.containsKey('class') &&
        attributes['class'] != null &&
        attributes['class'][ApiStrings.data] != null) {
      classModel = ClassModel.fromJson(attributes['class'][ApiStrings.data]);
    }

    EvaluationModel? evaluationModel;
    if (attributes.containsKey('evaluation') &&
        attributes['evaluation'] != null &&
        attributes['evaluation'][ApiStrings.data] != null) {
      evaluationModel =
          EvaluationModel.fromJson(attributes['evaluation'][ApiStrings.data]);
    }

    return EvaluationAnswerModel(
      id: json[ApiStrings.id],
      answers: answersList,
      studentName: attributes['student_name'] ?? '',
      parent: parentUser,
      classModel: classModel,
      evaluation: evaluationModel,
      createdAt: attributes[ApiStrings.createdAt] != null
          ? DateTime.parse(attributes[ApiStrings.createdAt]).toLocal()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      'answers': answers.map((a) => a.toJson()).toList(),
      'student_name': studentName,
      if (parent != null) 'parent': parent!.id,
      if (classModel != null) 'class': classModel!.id,
      // if (evaluation != null) 'evaluation': evaluation!.id,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId(),
    };
  }

  EvaluationAnswerModel copyWith({
    int? id,
    List<EvaluationQuestionModel>? answers,
    String? studentName,
    UserModel? parent,
    ClassModel? classModel,
    EvaluationModel? evaluation,
    DateTime? createdAt,
  }) {
    return EvaluationAnswerModel(
      id: id ?? this.id,
      answers: answers ?? this.answers,
      studentName: studentName ?? this.studentName,
      parent: parent ?? this.parent,
      classModel: classModel ?? this.classModel,
      evaluation: evaluation ?? this.evaluation,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        answers,
        studentName,
        parent,
        classModel,
        evaluation,
        createdAt,
      ];
}
