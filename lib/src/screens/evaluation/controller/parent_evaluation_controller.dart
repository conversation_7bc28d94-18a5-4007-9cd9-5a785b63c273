import 'dart:developer';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_answer_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/screens/evaluation/repo/evaluation_repo.dart';

import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';
import '../../nursery/models/nursery_model_helper.dart';

//? Parent Evaluation Repository Provider ========================================================
final parentEvaluationRepoProvider = Provider<EvaluationRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);
  return EvaluationRepo(networkApiService);
});

//? Parent Evaluation Controller Provider ========================================================
final parentEvaluationChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<ParentEvaluationController, BuildContext>(
        (ref, context) {
  final evaluationRepo = ref.watch(parentEvaluationRepoProvider);
  return ParentEvaluationController(context, evaluationRepo);
});

//? Get Pending Evaluations for Parent Provider ========================================================
final getPendingEvaluationsForParentProvider =
    FutureProvider.family<List<EvaluationModel>, BuildContext>((ref, context) {
  final controller =
      ref.watch(parentEvaluationChangeNotifierControllerProvider(context));
  return controller.getPendingEvaluationsForParent();
});

class ParentEvaluationController extends BaseVM {
  final BuildContext context;
  final EvaluationRepo evaluationRepo;

  ParentEvaluationController(this.context, this.evaluationRepo);

  //? Get Current Parent ID ========================================================
  int? get currentParentId => const UserModel().currentUser.id;

  //? Get Current Student ID ========================================================
  int get currentStudentId => UserModel.studentId();

  //? Get Pending Evaluations for Parent ========================================================
  Future<List<EvaluationModel>> getPendingEvaluationsForParent() async {
    return await baseFunction(context, () async {
      if (currentParentId == null) {
        log('Current parent ID is null');
        return [];
      }

      // Get all evaluations
      final allEvaluations = await evaluationRepo.getEvaluations();

      log('Found ${allEvaluations.length} pending evaluations for parent $currentParentId');
      return allEvaluations;
    });
  }

  //? Get Questions for Evaluation ========================================================
  List<EvaluationQuestionModel> getQuestionsForEvaluation(
      EvaluationModel evaluation) {
    // Return all questions since parent hasn't answered this evaluation yet
    return evaluation.questions;
  }

  //? Submit Evaluation Answers ========================================================
  Future<bool> submitEvaluationAnswers({
    required EvaluationModel evaluation,
    required List<EvaluationQuestionModel> answeredQuestions,
  }) async {
    return await baseFunction(context, () async {
      if (currentParentId == null) {
        throw Exception('Parent ID is null');
      }

      // Validate all questions are answered
      for (final question in answeredQuestions) {
        if (!_isQuestionValid(question)) {
          throw Exception('All questions must be answered');
        }
      }

      // Create evaluation answer model
      final evaluationAnswer = EvaluationAnswerModel(
        answers: answeredQuestions,
        studentName: selectedStudent.value?.name ??
            const UserModel()
                .currentUser
                .nameWithoutNumbersAndWithoutAnySpacedInLast,
        classModel: selectedStudent.value?.classModel,
        parent: const UserModel().currentUser,
        evaluation: evaluation,
      );

      // Submit the evaluation answer
      await evaluationRepo.addEvaluationAnswer(
        evaluationAnswer: evaluationAnswer,
      );

      // Update evaluation with current parent ID and student information in parent_ids
      final currentStudentId = selectedStudent.value?.id;
      final currentClassId = selectedStudent.value?.classModel?.id;

      if (currentStudentId != null && currentClassId != null) {
        // Find existing parent entry or create new one
        final existingParentIndex = evaluation.parentIds.indexWhere(
          (parentEntry) => parentEntry.parentId == currentParentId!,
        );

        List<ParentIdWithStudents> updatedParentIds = [...evaluation.parentIds];

        if (existingParentIndex != -1) {
          // Parent exists, add student to their list if not already present
          final existingParent = updatedParentIds[existingParentIndex];
          final studentExists = existingParent.studentIds.any(
            (student) => student.studentId == currentStudentId,
          );

          if (!studentExists) {
            final updatedStudentIds = [
              ...existingParent.studentIds,
              StudentIdWithClass(
                studentId: currentStudentId,
                classId: currentClassId,
              ),
            ];

            updatedParentIds[existingParentIndex] = ParentIdWithStudents(
              parentId: currentParentId!,
              studentIds: updatedStudentIds,
            );
          }
        } else {
          // Parent doesn't exist, create new entry
          updatedParentIds.add(
            ParentIdWithStudents(
              parentId: currentParentId!,
              studentIds: [
                StudentIdWithClass(
                  studentId: currentStudentId,
                  classId: currentClassId,
                ),
              ],
            ),
          );
        }

        final updatedEvaluation = evaluation.copyWith(
          parentIds: updatedParentIds,
        );

        // Edit evaluation to add parent ID with student information
        await evaluationRepo.editEvaluation(
          id: evaluation.id!,
          evaluation: updatedEvaluation,
        );
      }

      NotificationService.sendNotification(
        title: "New Evaluation Answer",
        body:
            "You have a new evaluation answer from ${selectedStudent.value?.name ?? const UserModel().currentUser.name}.",
        userTokenOrTopic: NurseryModelHelper.adminTopic(),
        isTopic: true,
      );

      log('You have a new evaluation answer from ${selectedStudent.value?.name ?? const UserModel().currentUser.name}.');
      return true;
    });
  }

  //? Validate Question Answer ========================================================
  bool _isQuestionValid(EvaluationQuestionModel question) {
    switch (question.type) {
      case QuestionType.rating:
        return question.rate != null && question.rate! > 0;
      case QuestionType.text:
        return question.answer != null && question.answer!.trim().isNotEmpty;
    }
  }

  //? Check if Question is Valid ========================================================
  bool isQuestionValid(EvaluationQuestionModel question) {
    return _isQuestionValid(question);
  }

  //? Get Validation Error Message ========================================================
  String? getValidationErrorMessage(EvaluationQuestionModel question) {
    if (_isQuestionValid(question)) return null;

    switch (question.type) {
      case QuestionType.rating:
        return context.tr.ratingRequired;
      case QuestionType.text:
        return context.tr.answerRequired;
    }
  }

  //? Check if All Questions are Valid ========================================================
  bool areAllQuestionsValid(List<EvaluationQuestionModel> questions) {
    return questions.every((question) => _isQuestionValid(question));
  }

  //? Get Progress Information ========================================================
  Map<String, int> getProgressInfo(List<EvaluationQuestionModel> questions) {
    final validQuestions = questions.where((q) => _isQuestionValid(q)).length;
    return {
      'completed': validQuestions,
      'total': questions.length,
    };
  }
}
