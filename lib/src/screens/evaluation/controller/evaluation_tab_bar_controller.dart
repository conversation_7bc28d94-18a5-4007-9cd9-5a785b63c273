import 'package:flutter_riverpod/flutter_riverpod.dart';

// * Evaluation Tab Bar Controller ========================================
final evaluationTabBarController = Provider<EvaluationTabBarController>(
  (ref) {
    return EvaluationTabBarController();
  },
);

// * Evaluation Tab Bar State Notifier ========================================
final evaluationTabBarControllerProvider =
    StateNotifierProvider<EvaluationTabBarController, int>(
  (ref) => ref.watch(evaluationTabBarController),
);

class EvaluationTabBarController extends StateNotifier<int> {
  EvaluationTabBarController() : super(0);

  void changeIndex(int index) {
    state = index;
  }
}
