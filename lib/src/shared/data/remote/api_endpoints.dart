import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';

class ApiEndpoints {
  static const String baseUrl =
      "https://connectify-rdlj3.ondigitalocean.app/api";

  // static const String baseUrl = "https://connectify.up.railway.app/api";

  //? Populate
  static const String populate = "?populate=deep";

  static final studentFilter = 'filters[student][id]=${UserModel.studentId()}';

  static final nurseryFilter = NurseryModelHelper.currentNurseryId() == null
      ? ''
      : '&filters[nursery][id]=${NurseryModelHelper.currentNurseryId()}';

  static final String populateWithFilter =
      "$populate&sort=createdAt:desc$nurseryFilter&$studentFilter";

  static String nurseryById(id) =>
      "$baseUrl/nurseries/$id?populate[logo]=*&populate[payment_methods]=*";

  static const String config = "$baseUrl/config$populate";

  static final String mood = "$baseUrl/moods$populateWithFilter";
  static const String limit = "pagination[pageSize]=10";
  static final String activityLevel =
      "$baseUrl/activity-levels$populateWithFilter";

  static String pagination(int page) => "pagination[page]=$page&$limit";

  // * users?populate=deep&filters[type][$eq]=teacher

  //? Class ------------------------
  static final String classPopulateWithFilter =
      "?populate[class_students][populate][image]=*&populate[class_students][populate][subscriptions]=*&populate[class_teachers][populate][image]=*&populate[logo]=*&sort=createdAt:desc$nurseryFilter";

  static final String classes = "$baseUrl/classes$classPopulateWithFilter";

  static const String editDeleteClass = "$baseUrl/classes";
  static const String postClass = "$baseUrl/classes";

  //? User ------------------------
  static const String auth = "$baseUrl/auth/local/register";
  static const String login = "$baseUrl/auth/local";

  static const String usersPopulateWithFilter =
      "?populate[teacher_classes][populate][logo]=*&populate[student_ids]=*&populate[nursery]=*";

  static const String users = "$baseUrl/users";
  static const String usersPopulate = "$baseUrl/users$usersPopulateWithFilter";

  static String usersById(int id) =>
      "$baseUrl/users/$id$usersPopulateWithFilter";

  static final String teacherPopulateWithFilter =
      "?populate[teacher_classes][populate][logo]=*&populate[image]=*&sort=createdAt:desc&filters[type]=teacher$nurseryFilter";

  static final String teachers = "$baseUrl/users$teacherPopulateWithFilter";

  static String teacherByClass(int classId) =>
      "$baseUrl/users$populateWithFilter&filters[type][\$eq]=teacher&filters[teacher_classes][\$in]=$classId";

  // static String teacherByClass(int classId) =>
  //     "$baseUrl/users$populateWithFilter&filters[type]=teacher&filters[class]=$classId";

  static String filterByDate(DateTime date) {
    final firstOfTheDay = DateTime(date.year, date.month, date.day, 0, 0, 0);
    final lastOfTheDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

    return "filters[createdAt][\$gte]=${firstOfTheDay.toIso8601String()}&filters[createdAt][\$lte]=${lastOfTheDay.toIso8601String()}";
  }

  //? Student ------------------------
  static const _onlyTeacherStudentsFilter = '';

  // const UserModel().selectedTeacherClassFilter;

  // static final String students = "$baseUrl/students";
  // static final String students = "$baseUrl/students$populate";
  static final String studentsPopulateWithFilter =
      "?populate[class]=*&populate[subscriptions]=*&populate[image]=*&sort=createdAt:desc$nurseryFilter$_onlyTeacherStudentsFilter";

  static final String studentsByIdFilter =
      "?populate[pickup_persons]=*&populate[subscriptions]=*&sort=createdAt:desc$nurseryFilter$_onlyTeacherStudentsFilter";

  static final String studentsByIdFromSplashFilter =
      "?sort=createdAt:desc$nurseryFilter$_onlyTeacherStudentsFilter";

  static final String students = "$baseUrl/students$studentsPopulateWithFilter";

  static final String activeStudents =
      "$baseUrl/active-students-count?sort=createdAt:desc$nurseryFilter$_onlyTeacherStudentsFilter";

  static String studentsPaginated(int page) =>
      "$students&pagination[page]=$page&pagination[pageSize]=14";

  static const String editDeleteStudents = "$baseUrl/students";

  // student by id
  static String studentById(id) => "$baseUrl/students/$id$studentsByIdFilter";

  static String studentByIdFromSplash(id) =>
      "$baseUrl/students/$id$studentsByIdFromSplashFilter";

  //studentsByIds
  static String studentsByIds(List<int> ids) {
    final filterParams = ids.map((id) => 'filters[id][\$in]=$id').join('&');
    return "$baseUrl/students$studentsPopulateWithFilter&$filterParams";
  }

  //? Activities ------------------------
  static final String activitiesPopulateWithFilter =
      "?populate[image]=*&sort=createdAt:desc$nurseryFilter";

  static final String activities =
      "$baseUrl/activities$activitiesPopulateWithFilter";
  static const String editDeleteActivities = "$baseUrl/activities";

  //? Notification ------------------------
  static final String notification =
      "$baseUrl/notifications$populate&sort=createdAt:desc&$nurseryFilter";
  static const String sendNotification = "$baseUrl/notifications";

  //? Food ------------------------
  static final String food =
      "$baseUrl/foods?sort=createdAt:desc&$studentFilter";
  static const String editDeleteFood = "$baseUrl/foods";
  static final String meal =
      "$baseUrl/food-meals?sort=createdAt:desc$nurseryFilter";

  //? Food ------------------------
  // teacher supply populate only
  // final List<SupplyModel>? supplies;
  // final StudentModel? student;
  // final TeacherModel? teacher;
  // final NurseryModel? nursery;
  static final String teacherSupplyPopulateWithFilter =
      "?populate[supplies]=*&populate[student]=*&populate[teacher]=*&sort=createdAt:desc$nurseryFilter&$studentFilter";
  static final String teacherSupply =
      "$baseUrl/teacher-supplies$teacherSupplyPopulateWithFilter";
  static const String editDeleteTeacherSupply = "$baseUrl/teacher-supplies";

  //? Food ------------------------
  static final String sleep =
      "$baseUrl/sleeps?sort=createdAt:desc$nurseryFilter&$studentFilter";
  static const String editDeleteSleeps = "$baseUrl/sleeps";

  //? Toilets ------------------------
  static final String toilets =
      "$baseUrl/toilets?sort=createdAt:desc&$studentFilter";
  static const String editDeleteToilet = "$baseUrl/toilets";

  //? exams ------------------------
  static final String examsPopulateWithFilter =
      "?populate[teacher]=*&populate[class]=*&populate[students_result][populate][student][populate][image]=*&sort=createdAt:asc$nurseryFilter";

  static final String exams = "$baseUrl/exams$examsPopulateWithFilter";
  static const String deleteExams = "$baseUrl/exams";

  //? Teacher Activities ------------------------
  static final String teacherActivitiesPopulateWithFilter =
      "?populate[teacher][populate][logo]=*&populate[activity_notes][populate][media]=*&populate[activity][populate][image]=*&populate[teacher]=*&populate[class][populate][logo]=*&sort=createdAt:desc$nurseryFilter&filters[class]=${UserModel.classId()}";

  static final String teacherActivities =
      "$baseUrl/teacher-activities$teacherActivitiesPopulateWithFilter";

  static String teacherActivitiesByClass(int classId) =>
      "$baseUrl/teacher-activities$populateWithFilter&filters[class]=$classId";

  static const String editDeleteTeacherActivities =
      "$baseUrl/teacher-activities";

  //? Attendance ------------------------
  static final String attendancesPopulateWithFilter =
      "?populate[teacher]=*&populate[class]=*&populate[student]=*&sort=createdAt:desc$nurseryFilter&$studentFilter";

  static final String attendances =
      "$baseUrl/attendances$attendancesPopulateWithFilter";
  static const String deleteAttendances = "$baseUrl/attendances";

  //? Bills ------------------------
  static final String bills =
      "$baseUrl/bills?sort=createdAt:desc$nurseryFilter";
  static const String deleteBills = "$baseUrl/bills";

  //? Invoices ------------------------
  static final String invoices =
      "$baseUrl/invoices?sort=createdAt:desc$nurseryFilter";
  static const String deleteInvoices = "$baseUrl/invoices";

  //? Subscriptions ------------------------
  static final String subscriptionPopulateWithFilter =
      "?populate[subscriptions]=*&sort=createdAt:desc$nurseryFilter";

  static final String subscriptions =
      "$baseUrl/subscriptions$populateWithFilter";
  static final String subscriptionStudents =
      "$baseUrl/students$subscriptionPopulateWithFilter";
  static const String deleteSubscriptions = "$baseUrl/subscriptions";

  //? Events ------------------------
  static final String eventsPopulateWithFilter =
      "?populate[teachers]=*&populate[selected_classes]=*&sort=createdAt:desc$nurseryFilter";

  static final String events = "$baseUrl/events$eventsPopulateWithFilter";
  static const String editDeleteEvent = "$baseUrl/events";

  //? supply ------------------------
  static final String supply = "$baseUrl/supplies$populateWithFilter";

  // "$baseUrl/supplies$populateWithFilter";
  static const String editDeleteSupply = "$baseUrl/supplies";

  //? Nursery ------------------------
  static const String nursery = "$baseUrl/nurseries$populate";

  static String filteredNurseryByAdminId(int? adminId) =>
      "$baseUrl/nurseries$populate&filters[admin]=$adminId";
  static const String updateNursery = "$baseUrl/nurseries";

  //? Messages ------------------------
  static final String messagesPopulateWithFilter =
      "?populate[admin]=*&populate[teacher]=*&populate[student][populate][image]=*&sort=createdAt:desc&$studentFilter";

  static final String messagesWithoutPopulate =
      "$baseUrl/message-centers?sort=createdAt:desc&$studentFilter&pagination[pageSize]=2";
  static final String messages =
      "$baseUrl/message-centers$messagesPopulateWithFilter";

  // "$populate&sort=createdAt:desc&$studentFilter";
  static const String addMessage = "$baseUrl/message-centers";

  //? Announcements ------------------------
  static final String announcementsPopulateWithFilter = "?sort=createdAt:desc"
      "&filters[\$or][0][target][\$eq]=all"
      "&filters[\$or][1][target][\$eq]=parents"
      "&filters[\$and][0][\$or][0][nursery][id][\$eq]=${NurseryModelHelper.currentNurseryId() ?? ''}"
      "&filters[\$and][0][\$or][1][nursery][admin][email][\$eq]=<EMAIL>";

  static final String announcements =
      "$baseUrl/announcements$announcementsPopulateWithFilter";
  static final String announcementsLimitedBy2 =
      "$baseUrl/announcements$announcementsPopulateWithFilter&pagination[pageSize]=2";
  static const String editDeleteAnnouncement = "$baseUrl/announcements";

  // plans
  static final String plansPopulateWithFilter =
      "?populate[class]=*&populate[sections][populate][image]=*&sort=createdAt:desc$nurseryFilter";
  static final String plans = "$baseUrl/plans$plansPopulateWithFilter";

  //? Evaluations ------------------------
  static final String evaluationsPopulateWithFilter =
      "?populate[classes]=*&populate[questions]=*&sort=createdAt:desc$nurseryFilter";
  static String evaluations(int? parentId) =>
      "$baseUrl/evaluations-by-parent-id/$parentId$evaluationsPopulateWithFilter";
  static const String editDeleteEvaluation = "$baseUrl/evaluations";

  //? Evaluation Answers ------------------------
  static final String evaluationAnswersPopulateWithFilter =
      "?populate[evaluation]=*&populate[parent]=*&sort=createdAt:desc$nurseryFilter";
  static final String evaluationAnswers =
      "$baseUrl/evaluations-answers$evaluationAnswersPopulateWithFilter";
  static const String editDeleteEvaluationAnswer =
      "$baseUrl/evaluations-answers";

  //? Parent Combined APIs ------------------------
  static String parentActivities({
    Map<String, dynamic>? filters,
    String? sort,
  }) {
    final queryParams = <String, String>{};

    // Add default filters
    queryParams['filters[student][id]'] = '${UserModel.studentId()}';
    if (NurseryModelHelper.currentNurseryId() != null) {
      queryParams['filters[nursery][id]'] =
          '${NurseryModelHelper.currentNurseryId()}';
    }

    // Add default filters
    queryParams['filters[class][id]'] = '${UserModel.classId()}';

    // Add custom filters
    if (filters != null) {
      _buildFilterParams(filters, queryParams);
    }

    // Add sorting
    queryParams['sort'] = sort ?? 'createdAt:desc';

    final uri = Uri.parse('$baseUrl/parent-activities');
    return uri.replace(queryParameters: queryParams).toString();
  }

  static String parentHome({
    Map<String, dynamic>? filters,
  }) {
    final queryParams = <String, String>{};

    queryParams['filters[student][id]'] = '${UserModel.studentId()}';

    if (NurseryModelHelper.currentNurseryId() != null) {
      queryParams['filters[nursery][id]'] =
          '${NurseryModelHelper.currentNurseryId()}';
    }

    // Add custom filters
    if (filters != null) {
      _buildFilterParams(filters, queryParams);
    }

    final uri = Uri.parse('$baseUrl/parent-home');

    final url = uri.replace(queryParameters: queryParams).toString();

    return url;
  }

  static void _buildFilterParams(
      Map<String, dynamic> filters, Map<String, String> queryParams) {
    filters.forEach((key, value) {
      if (value != null) {
        if (value is Map<String, dynamic>) {
          value.forEach((subKey, subValue) {
            if (subValue != null) {
              queryParams['filters[$key][$subKey]'] = subValue.toString();
            }
          });
        } else if (value is List) {
          for (int i = 0; i < value.length; i++) {
            if (value[i] is Map<String, dynamic>) {
              (value[i] as Map<String, dynamic>).forEach((subKey, subValue) {
                if (subValue != null) {
                  queryParams['filters[\$or][$i][$subKey]'] =
                      subValue.toString();
                }
              });
            } else {
              queryParams['filters[$key][$i]'] = value[i].toString();
            }
          }
        } else {
          queryParams['filters[$key]'] = value.toString();
        }
      }
    });
  }
}
