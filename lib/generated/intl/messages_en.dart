// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(completed, total) => "Completed: ${completed} of ${total}";

  static String m1(count) => "You have ${count} pending evaluations";

  static String m2(current, total) => "Question ${current} of ${total}";

  static String m3(name) => "Send a new message to ${name}";

  static String m4(Student) => "${Student} is absent today";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "Of": MessageLookupByLibrary.simpleMessage("Of"),
    "SignupAsa": MessageLookupByLibrary.simpleMessage("Signup as a"),
    "SkipForNow": MessageLookupByLibrary.simpleMessage("Skip for now"),
    "absent": MessageLookupByLibrary.simpleMessage("absent"),
    "active": MessageLookupByLibrary.simpleMessage("Active"),
    "activities": MessageLookupByLibrary.simpleMessage("Activities"),
    "activitiesCompleted": MessageLookupByLibrary.simpleMessage(
      "Activities completed",
    ),
    "activityChart": MessageLookupByLibrary.simpleMessage("Activity chart"),
    "activityDescription": MessageLookupByLibrary.simpleMessage(
      "Activity Description",
    ),
    "activityLevel": MessageLookupByLibrary.simpleMessage("Activity Level"),
    "activityName": MessageLookupByLibrary.simpleMessage("Activity Name"),
    "add": MessageLookupByLibrary.simpleMessage("Add"),
    "addANewStaffMember": MessageLookupByLibrary.simpleMessage(
      "Add a new staff member",
    ),
    "addNewBill": MessageLookupByLibrary.simpleMessage("Add New Bill"),
    "addNewClass": MessageLookupByLibrary.simpleMessage("Add New Class"),
    "addNewEvent": MessageLookupByLibrary.simpleMessage("Add New Event"),
    "addNewInvoice": MessageLookupByLibrary.simpleMessage("Add New Invoice"),
    "addNewQuestion": MessageLookupByLibrary.simpleMessage("Add New Question"),
    "addNewStudents": MessageLookupByLibrary.simpleMessage("Add New Students"),
    "addNurseryActivities": MessageLookupByLibrary.simpleMessage(
      "Add New Activity ",
    ),
    "addNurseryTeam": MessageLookupByLibrary.simpleMessage("Add Nursery Team"),
    "addNurseryTeamMember": MessageLookupByLibrary.simpleMessage(
      "Add New Team Member",
    ),
    "addParentsPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Add Parents Phone number",
    ),
    "addPickupPerson": MessageLookupByLibrary.simpleMessage(
      "Add Pickup Person",
    ),
    "addStudents": MessageLookupByLibrary.simpleMessage("Add Students"),
    "addTheEmergency": MessageLookupByLibrary.simpleMessage(
      "Add the emergency data that helps the nursery reach you out",
    ),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Added successfully",
    ),
    "address": MessageLookupByLibrary.simpleMessage("Address"),
    "admin": MessageLookupByLibrary.simpleMessage("Admin"),
    "adminSignUp": MessageLookupByLibrary.simpleMessage(
      "Administrator Sign up",
    ),
    "administrator": MessageLookupByLibrary.simpleMessage("Administrator"),
    "all": MessageLookupByLibrary.simpleMessage("All"),
    "allQuestionsRequired": MessageLookupByLibrary.simpleMessage(
      "All questions are required",
    ),
    "alreadyHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "Already have an account?",
    ),
    "amount": MessageLookupByLibrary.simpleMessage("Amount"),
    "and": MessageLookupByLibrary.simpleMessage("and"),
    "angry": MessageLookupByLibrary.simpleMessage("Angry"),
    "announcements": MessageLookupByLibrary.simpleMessage("Announcements"),
    "answerEvaluation": MessageLookupByLibrary.simpleMessage(
      "Answer Evaluation",
    ),
    "answerNow": MessageLookupByLibrary.simpleMessage("Answer Now"),
    "answerRequired": MessageLookupByLibrary.simpleMessage(
      "Answer is required",
    ),
    "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
    "areYouSureToDeleteThisActivity": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this activity ?",
    ),
    "areYouSureToDeleteThisBill": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this bill ?",
    ),
    "areYouSureToDeleteThisClass": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this class ?",
    ),
    "areYouSureToDeleteThisEvent": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this event ?",
    ),
    "areYouSureToDeleteThisInvoice": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this invoice ?",
    ),
    "areYouSureToDeleteThisQuestion": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this question ?",
    ),
    "areYouSureToDeleteThisStudent": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this student ?",
    ),
    "areYouSureToDeleteThisSupply": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this supply ?",
    ),
    "areYouSureToDeleteThisTeacher": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this teacher ?",
    ),
    "areYouSureToDeleteYourAccount": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete your account?",
    ),
    "assign": MessageLookupByLibrary.simpleMessage("Assign"),
    "assignActivityToClass": MessageLookupByLibrary.simpleMessage(
      "Assign Activity To Class",
    ),
    "assignSupplyToStudent": MessageLookupByLibrary.simpleMessage(
      "Assign Supply To Student",
    ),
    "assignToClass": MessageLookupByLibrary.simpleMessage("Assign to class"),
    "assigned": MessageLookupByLibrary.simpleMessage("Assigned"),
    "attendance": MessageLookupByLibrary.simpleMessage("Attendance"),
    "attendanceChart": MessageLookupByLibrary.simpleMessage("Attendance chart"),
    "attendanceTracking": MessageLookupByLibrary.simpleMessage(
      "Attendance Tracking",
    ),
    "attended": MessageLookupByLibrary.simpleMessage("attended"),
    "attendeesOfToday": MessageLookupByLibrary.simpleMessage(
      "Attendees of Today",
    ),
    "back": MessageLookupByLibrary.simpleMessage("Back"),
    "billAmount": MessageLookupByLibrary.simpleMessage("Bill Amount"),
    "billName": MessageLookupByLibrary.simpleMessage("Bill Name"),
    "bills": MessageLookupByLibrary.simpleMessage("Bills"),
    "billsChart": MessageLookupByLibrary.simpleMessage("Bills chart"),
    "birthDate": MessageLookupByLibrary.simpleMessage("Birth Date"),
    "breakfast": MessageLookupByLibrary.simpleMessage("Breakfast"),
    "calm": MessageLookupByLibrary.simpleMessage("Calm"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("Change Language"),
    "changePassword": MessageLookupByLibrary.simpleMessage("Change Password"),
    "classDescription": MessageLookupByLibrary.simpleMessage(
      "Class Description",
    ),
    "className": MessageLookupByLibrary.simpleMessage("Class Name"),
    "classes": MessageLookupByLibrary.simpleMessage("Classes"),
    "completeVerification": MessageLookupByLibrary.simpleMessage(
      "Complete Verification",
    ),
    "completedEvaluations": m0,
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "confirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "Confirm new password",
    ),
    "confirmation": MessageLookupByLibrary.simpleMessage("Confirmation"),
    "congratulations": MessageLookupByLibrary.simpleMessage("Congratulations"),
    "contactSupport": MessageLookupByLibrary.simpleMessage("Contact Support"),
    "copiedToClipboard": MessageLookupByLibrary.simpleMessage(
      "Copied to clipboard",
    ),
    "createANewClass": MessageLookupByLibrary.simpleMessage(
      "Create a new class",
    ),
    "createANewSupply": MessageLookupByLibrary.simpleMessage(
      "Add a new Supply",
    ),
    "createNewClass": MessageLookupByLibrary.simpleMessage("Create new class"),
    "currentActivity": MessageLookupByLibrary.simpleMessage("Current Activity"),
    "currentMonth": MessageLookupByLibrary.simpleMessage("Current month"),
    "dailySchedule": MessageLookupByLibrary.simpleMessage("Daily Schedule"),
    "dashboard": MessageLookupByLibrary.simpleMessage("Dashboard"),
    "date": MessageLookupByLibrary.simpleMessage("Date"),
    "day": MessageLookupByLibrary.simpleMessage("Day"),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete Account"),
    "deletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "deleted successfully",
    ),
    "description": MessageLookupByLibrary.simpleMessage("Description"),
    "didNotGetCode": MessageLookupByLibrary.simpleMessage(
      "Didn’t get the code?",
    ),
    "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "You don’t have account?",
    ),
    "downloadedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Downloaded successfully",
    ),
    "due": MessageLookupByLibrary.simpleMessage("Due"),
    "dueDate": MessageLookupByLibrary.simpleMessage("Due Date"),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "editClass": MessageLookupByLibrary.simpleMessage("Edit Class"),
    "editEvent": MessageLookupByLibrary.simpleMessage("Edit Event"),
    "editSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Edit successfully",
    ),
    "editTeacher": MessageLookupByLibrary.simpleMessage("Edit Teacher"),
    "editedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Edited successfully",
    ),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "emergency": MessageLookupByLibrary.simpleMessage("Emergency"),
    "emergencyInformation": MessageLookupByLibrary.simpleMessage(
      "Emergency Information",
    ),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "enter": MessageLookupByLibrary.simpleMessage("Enter"),
    "enterEtisalatCashNumber": MessageLookupByLibrary.simpleMessage(
      "Enter Etisalat Cash number",
    ),
    "enterInstapayNumberOrLink": MessageLookupByLibrary.simpleMessage(
      "Enter InstaPay number or link",
    ),
    "enterNewPassword": MessageLookupByLibrary.simpleMessage(
      "Enter new password",
    ),
    "enterOrangeCashNumber": MessageLookupByLibrary.simpleMessage(
      "Enter Orange Cash number",
    ),
    "enterOtp": MessageLookupByLibrary.simpleMessage("Enter OTP"),
    "enterPhoneNumberFirst": MessageLookupByLibrary.simpleMessage(
      "First enter your phone number",
    ),
    "enterPickupPerson": MessageLookupByLibrary.simpleMessage(
      "Enter pickup person",
    ),
    "enterValidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Enter valid phone number",
    ),
    "enterVodafoneCashNumber": MessageLookupByLibrary.simpleMessage(
      "Enter Vodafone Cash number",
    ),
    "enterWeCashNumber": MessageLookupByLibrary.simpleMessage(
      "Enter WE Cash number",
    ),
    "enterYourAnswerHere": MessageLookupByLibrary.simpleMessage(
      "Enter your answer here...",
    ),
    "errorLoadingEvaluations": MessageLookupByLibrary.simpleMessage(
      "Error loading evaluations",
    ),
    "errorOccurred": MessageLookupByLibrary.simpleMessage("Error occurred"),
    "evaluation": MessageLookupByLibrary.simpleMessage("Evaluation"),
    "evaluationAnswerSubmitted": MessageLookupByLibrary.simpleMessage(
      "Your answers have been submitted successfully",
    ),
    "evaluationCompleted": MessageLookupByLibrary.simpleMessage(
      "Evaluation Completed",
    ),
    "evaluationProgress": MessageLookupByLibrary.simpleMessage(
      "Evaluation Progress",
    ),
    "evaluationQuestions": MessageLookupByLibrary.simpleMessage(
      "Evaluation Questions",
    ),
    "evaluationSubmitted": MessageLookupByLibrary.simpleMessage(
      "Evaluation submitted successfully",
    ),
    "evaluations": MessageLookupByLibrary.simpleMessage("Evaluations"),
    "eventName": MessageLookupByLibrary.simpleMessage("Event Name"),
    "eventThisMonth": MessageLookupByLibrary.simpleMessage("Event this month"),
    "eventType": MessageLookupByLibrary.simpleMessage("Event Type"),
    "events": MessageLookupByLibrary.simpleMessage("Events"),
    "exams": MessageLookupByLibrary.simpleMessage("Exams"),
    "excited": MessageLookupByLibrary.simpleMessage("Excited"),
    "failedToUpdatePaymentMethods": MessageLookupByLibrary.simpleMessage(
      "Failed to update payment methods",
    ),
    "father": MessageLookupByLibrary.simpleMessage("Father"),
    "financial": MessageLookupByLibrary.simpleMessage("Financial"),
    "finishEvaluations": MessageLookupByLibrary.simpleMessage(
      "Finish Evaluations",
    ),
    "finishLetsStart": MessageLookupByLibrary.simpleMessage(
      "Finish, let’s start",
    ),
    "food": MessageLookupByLibrary.simpleMessage("Food"),
    "forgetPassword": MessageLookupByLibrary.simpleMessage("Forget Password"),
    "friday": MessageLookupByLibrary.simpleMessage("Friday"),
    "from": MessageLookupByLibrary.simpleMessage("From"),
    "getTalkingFrom": MessageLookupByLibrary.simpleMessage(
      "Every day is a journey. \nSign in to join us.",
    ),
    "goodAfternoon": MessageLookupByLibrary.simpleMessage("Good Afternoon!"),
    "goodMorning": MessageLookupByLibrary.simpleMessage("Good Morning!"),
    "happy": MessageLookupByLibrary.simpleMessage("Happy"),
    "high": MessageLookupByLibrary.simpleMessage("High"),
    "home": MessageLookupByLibrary.simpleMessage("Home"),
    "homeAddress": MessageLookupByLibrary.simpleMessage("Home Address"),
    "iHaveReadThe": MessageLookupByLibrary.simpleMessage("I have read the"),
    "inClothes": MessageLookupByLibrary.simpleMessage("Clothes"),
    "inTheDiaper": MessageLookupByLibrary.simpleMessage("Diaper"),
    "inTheToilet": MessageLookupByLibrary.simpleMessage("Toilet"),
    "incomeChart": MessageLookupByLibrary.simpleMessage("Income chart"),
    "invoiceAmount": MessageLookupByLibrary.simpleMessage("Invoice Amount"),
    "invoiceName": MessageLookupByLibrary.simpleMessage("Invoice Name"),
    "invoices": MessageLookupByLibrary.simpleMessage("Invoices "),
    "invoicesChart": MessageLookupByLibrary.simpleMessage("Invoices chart"),
    "later": MessageLookupByLibrary.simpleMessage("Later"),
    "letsDoAGreatJob": MessageLookupByLibrary.simpleMessage(
      "Let’s do a great job",
    ),
    "letsStart": MessageLookupByLibrary.simpleMessage("Let’s Start"),
    "loadingEvaluations": MessageLookupByLibrary.simpleMessage(
      "Loading evaluations...",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Logout"),
    "low": MessageLookupByLibrary.simpleMessage("Low"),
    "lunch": MessageLookupByLibrary.simpleMessage("Lunch"),
    "markAsRead": MessageLookupByLibrary.simpleMessage("Mark as Read"),
    "markAsSent": MessageLookupByLibrary.simpleMessage("Mark as Sent"),
    "matherPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Mather Phone number",
    ),
    "maxStudentsReachedPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "Max students reached, please contact support !",
        ),
    "maxUploadFileSizeIsOnly5MB": MessageLookupByLibrary.simpleMessage(
      "Max upload image size is only 5 MB",
    ),
    "maxUploadFilesIsOnly4": MessageLookupByLibrary.simpleMessage(
      "Max upload images is only 4",
    ),
    "mealAmount": MessageLookupByLibrary.simpleMessage("meal Amount"),
    "mealType": MessageLookupByLibrary.simpleMessage("Meal Type"),
    "medium": MessageLookupByLibrary.simpleMessage("Medium"),
    "members": MessageLookupByLibrary.simpleMessage("Members"),
    "message": MessageLookupByLibrary.simpleMessage("Message"),
    "messageSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Message sent successfully",
    ),
    "messages": MessageLookupByLibrary.simpleMessage("Messages"),
    "mobile": MessageLookupByLibrary.simpleMessage("Mobile"),
    "monday": MessageLookupByLibrary.simpleMessage("Monday"),
    "month": MessageLookupByLibrary.simpleMessage("Month"),
    "mood": MessageLookupByLibrary.simpleMessage("Mood"),
    "more": MessageLookupByLibrary.simpleMessage("more"),
    "mother": MessageLookupByLibrary.simpleMessage("Mother"),
    "motherPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Mother Phone number",
    ),
    "myClass": MessageLookupByLibrary.simpleMessage("My Class"),
    "myClasses": MessageLookupByLibrary.simpleMessage("My Classes"),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "newEvaluationAvailable": MessageLookupByLibrary.simpleMessage(
      "New Evaluation Available",
    ),
    "next": MessageLookupByLibrary.simpleMessage("Next"),
    "nextEvaluation": MessageLookupByLibrary.simpleMessage("Next Evaluation"),
    "nextPayment": MessageLookupByLibrary.simpleMessage("Next Payment"),
    "noActivities": MessageLookupByLibrary.simpleMessage("No Activities"),
    "noActivitiesFound": MessageLookupByLibrary.simpleMessage(
      "No activities found",
    ),
    "noActivitiesToday": MessageLookupByLibrary.simpleMessage(
      "No activities today",
    ),
    "noAnnouncements": MessageLookupByLibrary.simpleMessage("No Announcements"),
    "noAnnouncementsToday": MessageLookupByLibrary.simpleMessage(
      "No announcements today",
    ),
    "noBills": MessageLookupByLibrary.simpleMessage("No Bills"),
    "noClasses": MessageLookupByLibrary.simpleMessage("No Classes"),
    "noData": MessageLookupByLibrary.simpleMessage("No data"),
    "noEvaluationsFound": MessageLookupByLibrary.simpleMessage(
      "No evaluations found",
    ),
    "noEvents": MessageLookupByLibrary.simpleMessage("No events"),
    "noInvoices": MessageLookupByLibrary.simpleMessage("No Invoices"),
    "noNotifications": MessageLookupByLibrary.simpleMessage("No Notifications"),
    "noPlansForThisMonth": MessageLookupByLibrary.simpleMessage(
      "No plans for this month",
    ),
    "noQuestions": MessageLookupByLibrary.simpleMessage("No questions"),
    "noStudents": MessageLookupByLibrary.simpleMessage("No Students"),
    "noSupplies": MessageLookupByLibrary.simpleMessage("No Supplies"),
    "noTeachers": MessageLookupByLibrary.simpleMessage("No Teachers"),
    "none": MessageLookupByLibrary.simpleMessage("None"),
    "note": MessageLookupByLibrary.simpleMessage("Note"),
    "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "nurseryActivities": MessageLookupByLibrary.simpleMessage(
      "Add a Nursery Activities",
    ),
    "nurseryDataNotFound": MessageLookupByLibrary.simpleMessage(
      "Nursery data not found",
    ),
    "nurseryLogo": MessageLookupByLibrary.simpleMessage("Nursery Logo"),
    "nurseryName": MessageLookupByLibrary.simpleMessage("Nursery Name"),
    "outOf": MessageLookupByLibrary.simpleMessage("out of"),
    "parentPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Parent Phone number",
    ),
    "password": MessageLookupByLibrary.simpleMessage("Password"),
    "passwordConfirmation": MessageLookupByLibrary.simpleMessage(
      "Password Confirmation",
    ),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "Passwords do not match",
    ),
    "passwordsShouldMatch": MessageLookupByLibrary.simpleMessage(
      "Passwords should match",
    ),
    "payNow": MessageLookupByLibrary.simpleMessage("Pay Now"),
    "paymentDue": MessageLookupByLibrary.simpleMessage("Payment Due"),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("Payment Method"),
    "paymentMethods": MessageLookupByLibrary.simpleMessage("Payment Methods"),
    "paymentMethodsUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Payment methods updated successfully",
    ),
    "paymentPaid": MessageLookupByLibrary.simpleMessage("Payment Paid"),
    "paymentPending": MessageLookupByLibrary.simpleMessage("Payment Pending"),
    "paymentPendingApproval": MessageLookupByLibrary.simpleMessage(
      "Payment pending approval",
    ),
    "paymentScreenshot": MessageLookupByLibrary.simpleMessage(
      "Payment Screenshot",
    ),
    "paymentSubmittedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Payment submitted successfully and is pending approval",
    ),
    "pendingEvaluations": m1,
    "persons": MessageLookupByLibrary.simpleMessage("Persons"),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone Number"),
    "pickImage": MessageLookupByLibrary.simpleMessage("Pick Image"),
    "pickupPerson": MessageLookupByLibrary.simpleMessage("Pickup Person"),
    "pickupPersonAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "Pickup person already exists",
    ),
    "pickupPersons": MessageLookupByLibrary.simpleMessage("Pickup Persons"),
    "plans": MessageLookupByLibrary.simpleMessage("Plans"),
    "pleaseAcceptTerms": MessageLookupByLibrary.simpleMessage(
      "Please accept terms",
    ),
    "pleaseAnswerEvaluationQuestions": MessageLookupByLibrary.simpleMessage(
      "Please answer the nursery evaluation questions",
    ),
    "pleaseEnterAnswer": MessageLookupByLibrary.simpleMessage(
      "Please enter an answer",
    ),
    "pleasePickAnImage": MessageLookupByLibrary.simpleMessage(
      "Please pick an image",
    ),
    "pleaseSelectPaymentMethodAndUploadScreenshot":
        MessageLookupByLibrary.simpleMessage(
          "Please select a payment method and upload a screenshot",
        ),
    "pleaseSelectRating": MessageLookupByLibrary.simpleMessage(
      "Please select a rating",
    ),
    "pleaseUploadPaymentScreenshot": MessageLookupByLibrary.simpleMessage(
      "Please upload a screenshot of your payment confirmation",
    ),
    "pleaseUseConnectifyAdminTeachersApplication":
        MessageLookupByLibrary.simpleMessage(
          "Please use Connectify Admin & Teachers application",
        ),
    "pleaseVerifyPhone": MessageLookupByLibrary.simpleMessage(
      "Please verify phone",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "profile": MessageLookupByLibrary.simpleMessage("Profile"),
    "question": MessageLookupByLibrary.simpleMessage("Question"),
    "questionOf": m2,
    "ratingRequired": MessageLookupByLibrary.simpleMessage(
      "Rating is required",
    ),
    "reports": MessageLookupByLibrary.simpleMessage("Reports"),
    "required": MessageLookupByLibrary.simpleMessage("Required"),
    "requiredSupplies": MessageLookupByLibrary.simpleMessage(
      "Required supplies",
    ),
    "resendCode": MessageLookupByLibrary.simpleMessage("Resend Code"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "Reset your password",
    ),
    "results": MessageLookupByLibrary.simpleMessage("Results"),
    "retryLoadEvaluations": MessageLookupByLibrary.simpleMessage("Retry"),
    "sad": MessageLookupByLibrary.simpleMessage("Sad"),
    "saturday": MessageLookupByLibrary.simpleMessage("Saturday"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "savePaymentMethods": MessageLookupByLibrary.simpleMessage(
      "Save Payment Methods",
    ),
    "screenshotUploaded": MessageLookupByLibrary.simpleMessage(
      "Screenshot uploaded successfully",
    ),
    "searchQuestion": MessageLookupByLibrary.simpleMessage("Search Question"),
    "sections": MessageLookupByLibrary.simpleMessage("Sections"),
    "seeAll": MessageLookupByLibrary.simpleMessage("See all"),
    "seeAllActivities": MessageLookupByLibrary.simpleMessage(
      "See all activities",
    ),
    "seeAllAnnouncements": MessageLookupByLibrary.simpleMessage(
      "See all announcements",
    ),
    "selectPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "Select Payment Method",
    ),
    "selectPeriod": MessageLookupByLibrary.simpleMessage("Select period"),
    "send": MessageLookupByLibrary.simpleMessage("Send"),
    "sendANewMessage": MessageLookupByLibrary.simpleMessage(
      "Send a new message",
    ),
    "sendANewMessageTo": m3,
    "sendANewMessageToAdmin": MessageLookupByLibrary.simpleMessage(
      "Send message to\nAdmin",
    ),
    "sendANewMessageToTeacher": MessageLookupByLibrary.simpleMessage(
      "Send message to\nTeacher",
    ),
    "sendSupplies": MessageLookupByLibrary.simpleMessage("Send Supplies"),
    "sendSupplyToStudent": MessageLookupByLibrary.simpleMessage(
      "Send Supply To Student",
    ),
    "sent": MessageLookupByLibrary.simpleMessage("Sent"),
    "sentVerificationCode": MessageLookupByLibrary.simpleMessage(
      "We sent a verification code to",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "setupYourClasses": MessageLookupByLibrary.simpleMessage(
      "Setup your classes",
    ),
    "signIn": MessageLookupByLibrary.simpleMessage("Sign In"),
    "signUp": MessageLookupByLibrary.simpleMessage("Sign Up"),
    "singleActivity": MessageLookupByLibrary.simpleMessage("Single Activity"),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "skipForNow": MessageLookupByLibrary.simpleMessage("Skip for Now"),
    "sleep": MessageLookupByLibrary.simpleMessage("Sleep"),
    "sleepy": MessageLookupByLibrary.simpleMessage("Sleepy"),
    "snack": MessageLookupByLibrary.simpleMessage("Snack"),
    "some": MessageLookupByLibrary.simpleMessage("Some"),
    "speakWithConfidence": MessageLookupByLibrary.simpleMessage(
      "Speak with confidence",
    ),
    "staff": MessageLookupByLibrary.simpleMessage("Staff"),
    "stool": MessageLookupByLibrary.simpleMessage("Stool"),
    "student": MessageLookupByLibrary.simpleMessage("Student"),
    "studentAndClass": MessageLookupByLibrary.simpleMessage("Student & Class"),
    "studentIsAbsentToday": m4,
    "studentName": MessageLookupByLibrary.simpleMessage("Student Name"),
    "students": MessageLookupByLibrary.simpleMessage("Students"),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "submitAnswers": MessageLookupByLibrary.simpleMessage("Submit Answers"),
    "subscriptionExpiredPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "Subscription expired, please contact support !",
        ),
    "supplies": MessageLookupByLibrary.simpleMessage("Supplies"),
    "supplyName": MessageLookupByLibrary.simpleMessage("supply Name"),
    "tClass": MessageLookupByLibrary.simpleMessage("Class"),
    "teacher": MessageLookupByLibrary.simpleMessage("Teacher"),
    "teacherInfo": MessageLookupByLibrary.simpleMessage("Teacher info"),
    "teacherName": MessageLookupByLibrary.simpleMessage("Teacher Name"),
    "teacherSignUp": MessageLookupByLibrary.simpleMessage("Teacher Sign up"),
    "teachers": MessageLookupByLibrary.simpleMessage("Teachers"),
    "team": MessageLookupByLibrary.simpleMessage("Team"),
    "thankYouForFeedback": MessageLookupByLibrary.simpleMessage(
      "Thank you for your feedback",
    ),
    "thursday": MessageLookupByLibrary.simpleMessage("Thursday"),
    "title": MessageLookupByLibrary.simpleMessage("Title"),
    "to": MessageLookupByLibrary.simpleMessage("To"),
    "toAdmin": MessageLookupByLibrary.simpleMessage("To Admin"),
    "toTeacher": MessageLookupByLibrary.simpleMessage("To Teacher"),
    "today": MessageLookupByLibrary.simpleMessage("Today"),
    "todaysActivities": MessageLookupByLibrary.simpleMessage(
      "Today’s activities",
    ),
    "toilet": MessageLookupByLibrary.simpleMessage("Toilet"),
    "toiletType": MessageLookupByLibrary.simpleMessage("Toilet Type"),
    "transferNow": MessageLookupByLibrary.simpleMessage("Transfer Now"),
    "tuesday": MessageLookupByLibrary.simpleMessage("Tuesday"),
    "unAssign": MessageLookupByLibrary.simpleMessage("UnAssign"),
    "unwell": MessageLookupByLibrary.simpleMessage("Unwell"),
    "update": MessageLookupByLibrary.simpleMessage("Update"),
    "updateRequired": MessageLookupByLibrary.simpleMessage(
      "An update is required to continue using the app. Please update it now.",
    ),
    "updatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Updated successfully",
    ),
    "uploadLogo": MessageLookupByLibrary.simpleMessage("Upload logo"),
    "uploadPaymentScreenshot": MessageLookupByLibrary.simpleMessage(
      "Upload Payment Screenshot",
    ),
    "uploadScreenshot": MessageLookupByLibrary.simpleMessage(
      "Upload Screenshot",
    ),
    "urine": MessageLookupByLibrary.simpleMessage("Urine"),
    "userNotFound": MessageLookupByLibrary.simpleMessage("User not Found"),
    "validateYourPhoneFirstPlease": MessageLookupByLibrary.simpleMessage(
      "Validate your phone first please",
    ),
    "verificationCodeIsWrong": MessageLookupByLibrary.simpleMessage(
      "Verification code is wrong",
    ),
    "verificationSuccessful": MessageLookupByLibrary.simpleMessage(
      "Verification successful",
    ),
    "verify": MessageLookupByLibrary.simpleMessage("verify"),
    "warning": MessageLookupByLibrary.simpleMessage("Warning"),
    "wednesday": MessageLookupByLibrary.simpleMessage("Wednesday"),
    "weeklyActivity": MessageLookupByLibrary.simpleMessage("Weekly Activity"),
    "worried": MessageLookupByLibrary.simpleMessage("Worried"),
    "youCannotDeleteThisQuestionBecauseitsHasStudentResults":
        MessageLookupByLibrary.simpleMessage(
          "You cannot delete this question because it\'s has student results",
        ),
  };
}
